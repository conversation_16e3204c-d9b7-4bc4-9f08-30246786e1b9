# API Specification Fields in Document Model

This document describes the new API specification fields that have been added to the existing Document model to support API specifications alongside regular documents.

## Overview

The Document model has been extended with the following new fields to support API specifications:

- `isApiSpec`: Boolean flag to indicate if the document is an API specification
- `baseUrl`: The base URL of the API
- `apiVersion`: The version of the API
- `authType`: The type of authentication required (enum)
- `authConfig`: JSON object containing authentication configuration
- `endpoints`: JSON array containing endpoint definitions

## Database Schema Changes

### New Fields in Document Model

```prisma
model Document {
  // ... existing fields ...
  
  // API Specification fields
  isApiSpec    Boolean         @default(false) // Indicates if this document is an API specification
  baseUrl      String? // The base URL of the API (e.g., "https://api.example.com/v1")
  apiVersion   String? // The version of the API (e.g., "1.0.0", "v2")
  authType     ApiAuthType? // The type of authentication required
  authConfig   Json? // Authentication configuration (API keys, OAuth settings, etc.)
  endpoints    Json? // JSON array of endpoint definitions with methods, paths, parameters, etc.
  
  @@index([isApiSpec])
}
```

### New Enum: ApiAuthType

```prisma
enum ApiAuthType {
  NONE
  API_KEY
  BEARER_TOKEN
  BASIC_AUTH
  OAUTH2
  CUSTOM
}
```

## TypeScript Types

### Updated DocumentData Type

```typescript
export type DocumentData = {
  name: string;
  key: string;
  storageType: DocumentStorageType;
  contentType: string;
  supportedFileType: string; // Now includes "api" for API specifications
  fileSize: number | undefined;
  numPages?: number;
  enableExcelAdvancedMode?: boolean;
  
  // API Specification fields
  isApiSpec?: boolean;
  baseUrl?: string;
  apiVersion?: string;
  authType?: ApiAuthType;
  authConfig?: any; // JSON object for auth configuration
  endpoints?: any; // JSON array of endpoint definitions
};
```

## Usage Examples

### Creating an API Specification Document

```typescript
import { ApiAuthType, DocumentStorageType } from "@prisma/client";

const apiSpecData: DocumentData = {
  name: "Stripe API v1",
  key: "stripe-api-spec.json",
  storageType: DocumentStorageType.VERCEL_BLOB,
  contentType: "application/json",
  supportedFileType: "api",
  fileSize: 1024 * 50,
  
  // API Specification fields
  isApiSpec: true,
  baseUrl: "https://api.stripe.com/v1",
  apiVersion: "2023-10-16",
  authType: ApiAuthType.BEARER_TOKEN,
  authConfig: {
    type: "bearer",
    scheme: "bearer",
    description: "Use your Stripe secret key as the bearer token",
    headerName: "Authorization",
    prefix: "Bearer "
  },
  endpoints: [
    {
      path: "/customers",
      method: "GET",
      summary: "List all customers",
      parameters: [
        {
          name: "limit",
          in: "query",
          type: "integer",
          description: "A limit on the number of objects to be returned"
        }
      ],
      responses: {
        "200": {
          description: "A list of customers"
        }
      }
    }
  ]
};
```

### Different Authentication Types

#### API Key Authentication
```typescript
{
  authType: ApiAuthType.API_KEY,
  authConfig: {
    type: "apiKey",
    name: "X-API-Key",
    in: "header",
    description: "API key for authentication"
  }
}
```

#### OAuth2 Authentication
```typescript
{
  authType: ApiAuthType.OAUTH2,
  authConfig: {
    type: "oauth2",
    flow: "authorizationCode",
    authorizationUrl: "https://api.example.com/oauth/authorize",
    tokenUrl: "https://api.example.com/oauth/token",
    scopes: {
      "read": "Read access to resources",
      "write": "Write access to resources"
    }
  }
}
```

## API Endpoints

### Creating an API Specification Document

```http
POST /api/teams/{teamId}/documents
Content-Type: application/json

{
  "name": "My API Specification",
  "url": "api-spec-file-url",
  "storageType": "VERCEL_BLOB",
  "type": "api",
  "contentType": "application/json",
  "isApiSpec": true,
  "baseUrl": "https://api.example.com/v1",
  "apiVersion": "1.0.0",
  "authType": "BEARER_TOKEN",
  "authConfig": {
    "type": "bearer",
    "description": "Bearer token authentication"
  },
  "endpoints": [
    {
      "path": "/users",
      "method": "GET",
      "summary": "List users"
    }
  ]
}
```

## Migration

The database migration `20250618105906_add_api_specification_fields` has been applied, which:

1. Creates the `ApiAuthType` enum
2. Adds the new fields to the Document table
3. Creates an index on the `isApiSpec` field for efficient querying

## Backward Compatibility

All new fields are optional and have sensible defaults:
- `isApiSpec` defaults to `false`
- All other fields are nullable

This ensures that existing documents continue to work without any changes.

## Querying API Specifications

To query only API specification documents:

```typescript
const apiSpecs = await prisma.document.findMany({
  where: {
    teamId: "your-team-id",
    isApiSpec: true
  }
});
```

To query regular documents (non-API specs):

```typescript
const regularDocs = await prisma.document.findMany({
  where: {
    teamId: "your-team-id",
    isApiSpec: false
  }
});
```
