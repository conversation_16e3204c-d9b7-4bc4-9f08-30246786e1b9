import { useEffect, useRef, useState } from "react";

interface StoplightElementsWrapperProps {
  specification: any;
  baseUrl?: string;
}

export default function StoplightElementsWrapper({ 
  specification, 
  baseUrl 
}: StoplightElementsWrapperProps) {
  const elementRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!specification || !elementRef.current) {
      setLoading(false);
      return;
    }

    const loadStoplightElements = async () => {
      try {
        setLoading(true);
        setError(null);

        // Import Stoplight Elements
        const { API } = await import("@stoplight/elements");
        
        // Clear any existing content
        if (elementRef.current) {
          elementRef.current.innerHTML = "";
        }

        // Create the API element with configuration
        const apiElement = new API({
          apiDescriptionDocument: specification,
          basePath: "/",
          router: "hash",
          layout: "responsive",
          hideInternal: false,
          hideSchemas: false,
          hideExport: false,
          tryItCredentialsPolicy: "include",
          tryItCorsProxy: undefined,
        });

        // Mount the element
        if (elementRef.current) {
          elementRef.current.appendChild(apiElement);
        }

        setLoading(false);
      } catch (error) {
        console.error("Error loading Stoplight Elements:", error);
        setError(error instanceof Error ? error.message : "Failed to load API documentation");
        setLoading(false);
      }
    };

    loadStoplightElements();

    // Cleanup function
    return () => {
      if (elementRef.current) {
        elementRef.current.innerHTML = "";
      }
    };
  }, [specification, baseUrl]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64 border rounded-lg">
        <div className="text-center space-y-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-sm text-muted-foreground">Loading API documentation...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 border rounded-lg bg-red-50 dark:bg-red-950">
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">
              Failed to Load API Documentation
            </h3>
            <p className="text-sm text-red-600 dark:text-red-300 mt-1">
              {error}
            </p>
          </div>
          
          <details className="space-y-2">
            <summary className="text-sm font-medium text-red-700 dark:text-red-300 cursor-pointer">
              View Raw Specification
            </summary>
            <pre className="bg-red-100 dark:bg-red-900 p-4 rounded text-xs overflow-auto max-h-96 text-red-800 dark:text-red-200">
              {JSON.stringify(specification, null, 2)}
            </pre>
          </details>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div 
        ref={elementRef} 
        className="min-h-[600px] w-full"
        style={{
          // Ensure Stoplight Elements styles don't conflict with our app
          isolation: "isolate",
        }}
      />
      
      {/* Custom styles for Stoplight Elements */}
      <style jsx global>{`
        /* Ensure Stoplight Elements integrates well with our theme */
        .sl-elements {
          --color-primary: rgb(59 130 246);
          --color-primary-light: rgb(147 197 253);
          --color-primary-dark: rgb(29 78 216);
        }
        
        /* Dark mode adjustments */
        .dark .sl-elements {
          --color-canvas: rgb(3 7 18);
          --color-canvas-100: rgb(17 24 39);
          --color-canvas-200: rgb(31 41 55);
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
          .sl-elements .sl-stack {
            flex-direction: column;
          }
        }
      `}</style>
    </div>
  );
}
