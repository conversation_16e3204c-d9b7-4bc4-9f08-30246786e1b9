datasource db {
  provider          = "postgresql"
  url               = env("POSTGRES_PRISMA_URL") // uses connection pooling
  directUrl         = env("POSTGRES_PRISMA_URL_NON_POOLING") // uses a direct connection
  shadowDatabaseUrl = env("POSTGRES_PRISMA_SHADOW_URL") // used for migrations
}

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["relationJoins", "prismaSchemaFolder"]
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id             String     @id @default(cuid())
  name           String?
  email          String?    @unique
  emailVerified  DateTime?
  image          String?
  createdAt      DateTime   @default(now())
  accounts       Account[]
  sessions       Session[]
  documents      Document[]
  teams          UserTeam[]
  domains        Domain[]
  chats          Chat[]
  contactId      String?
  plan           String     @default("free")
  stripeId       String?    @unique // Stripe subscription / customer ID
  subscriptionId String?    @unique // Stripe subscription ID
  startsAt       DateTime? // Stripe subscription start date
  endsAt         DateTime? // Stripe subscription end date

  restrictedTokens RestrictedToken[]

  // conversation
  participatedConversations ConversationParticipant[]
  messages                  Message[]
}

model Team {
  id           String        @id @default(cuid())
  name         String
  users        UserTeam[]
  documents    Document[]
  folders      Folder[]
  domains      Domain[]
  invitations  Invitation[]
  sentEmails   SentEmail[]
  brand        Brand?
  datarooms    Dataroom[]
  agreements   Agreement[]
  viewerGroups ViewerGroup[]
  viewers      Viewer[]

  permissionGroups PermissionGroup[]

  links Link[]
  views View[]

  plan           String    @default("free")
  stripeId       String?   @unique // Stripe customer ID
  subscriptionId String?   @unique // Stripe subscription ID
  startsAt       DateTime? // Stripe subscription start date
  endsAt         DateTime? // Stripe subscription end date

  linkPresets LinkPreset[] // Link presets for the team

  limits Json? // Plan limits // {datarooms: 1, users: 1, domains: 1, customDomainOnPro: boolean, customDomainInDataroom: boolean}

  incomingWebhooks IncomingWebhook[]
  restrictedTokens RestrictedToken[]
  webhooks         Webhook[]

  conversations Conversation[]

  uploadedDocuments DocumentUpload[]

  // team settings
  enableExcelAdvancedMode Boolean @default(false) // Enable Excel advanced mode for all documents in the team

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  Tag       Tag[]
}

model Brand {
  id          String  @id @default(cuid())
  logo        String? // This should be a reference to where the file is stored (S3, Google Cloud Storage, etc.)
  brandColor  String? // This should be a reference to the brand color
  accentColor String? // This should be a reference to the accent color
  teamId      String  @unique
  team        Team    @relation(fields: [teamId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum Role {
  ADMIN
  MANAGER
  MEMBER
}

model UserTeam {
  role   Role   @default(MEMBER)
  status String @default("ACTIVE")
  userId String
  teamId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  team   Team   @relation(fields: [teamId], references: [id], onDelete: Cascade)

  blockedAt               DateTime? // When the user was blocked
  notificationPreferences Json? // Format: { yearInReview: { "enabled": false } }

  @@id([userId, teamId])
  @@index([userId])
  @@index([teamId])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Document {
  id                   String              @id @default(cuid())
  name                 String
  description          String?
  file                 String // This should be a reference to where the file is stored (S3, Google Cloud Storage, etc.)
  originalFile         String? // This should be a reference to the original file like pptx, xlsx, etc. (S3, Google Cloud Storage, etc.)
  type                 String? // This should be a reference to the file type (pdf, sheet, etc.)
  contentType          String? // This should be the actual contentType of the file like application/pdf, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, etc.
  storageType          DocumentStorageType @default(VERCEL_BLOB)
  numPages             Int? // This should be a reference to the number of pages in the document
  owner                User?               @relation(fields: [ownerId], references: [id], onDelete: SetNull)
  teamId               String
  team                 Team                @relation(fields: [teamId], references: [id], onDelete: Cascade)
  ownerId              String? // This field holds the foreign key.
  assistantEnabled     Boolean             @default(false) // This indicates if assistant is enabled for this document
  advancedExcelEnabled Boolean             @default(false) // This indicates if advanced Excel is enabled for this document
  downloadOnly         Boolean             @default(false) // Indicates if the document is download only
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt
  links                Link[]
  views                View[]
  versions             DocumentVersion[]
  chats                Chat[]

  folderId String? // Optional Folder ID for documents in folders
  folder   Folder? @relation(fields: [folderId], references: [id], onDelete: SetNull)

  datarooms DataroomDocument[] // Datarooms associated with this document
  tags      TagItem[]

  // upload external documents
  uploadedDocument DocumentUpload[]
  isExternalUpload Boolean          @default(false) // Indicates if the document is an external upload

  // API Specification fields
  isApiSpec    Boolean         @default(false) // Indicates if this document is an API specification
  baseUrl      String? // The base URL of the API (e.g., "https://api.example.com/v1")
  apiVersion   String? // The version of the API (e.g., "1.0.0", "v2")
  authType     ApiAuthType? // The type of authentication required
  authConfig   Json? // Authentication configuration (API keys, OAuth settings, etc.)
  endpoints    Json? // JSON array of endpoint definitions with methods, paths, parameters, etc.

  @@index([ownerId])
  @@index([teamId])
  @@index([folderId])
  @@index([isApiSpec])
}

model DocumentVersion {
  id            String              @id @default(cuid())
  versionNumber Int // e.g., 1, 2, 3 for version control
  document      Document            @relation(fields: [documentId], references: [id], onDelete: Cascade)
  documentId    String
  file          String // This should be a reference to where the file is stored (S3, Google Cloud Storage, etc.)
  originalFile  String? // This should be a reference to the original file like pptx, xlsx, etc. (S3, Google Cloud Storage, etc.)
  type          String? // This should be a reference to the file type (pdf, docx, etc.)
  contentType   String? // This should be the actual contentType of the file like application/pdf, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, etc.
  fileSize      Int? // This should be the size of the file in bytes
  storageType   DocumentStorageType @default(VERCEL_BLOB)
  numPages      Int? // This should be a reference to the number of pages in the document
  isPrimary     Boolean             @default(false) // Indicates if this is the primary version
  isVertical    Boolean             @default(false) // Indicates if the document is vertical (portrait) or not (landscape)
  fileId        String? // This is the file ID of the OpenAI File API
  pages         DocumentPage[]
  hasPages      Boolean             @default(false) // Indicates if the document has pages
  length        Int? // This is the length of the video in seconds
  createdAt     DateTime            @default(now())
  updatedAt     DateTime            @updatedAt

  @@unique([versionNumber, documentId])
  @@index([documentId])
}

model DocumentPage {
  id            String              @id @default(cuid())
  version       DocumentVersion     @relation(fields: [versionId], references: [id], onDelete: Cascade)
  versionId     String
  pageNumber    Int // e.g., 1, 2, 3 for 
  embeddedLinks String[]
  pageLinks     Json? // This will store the page links data: [{href: "https://example.com", coords: "0,0,100,100"}]
  metadata      Json? // This will store the page metadata: {originalWidth: 100, origianlHeight: 100, scaledWidth: 50, scaledHeight: 50, scaleFactor: 2}
  file          String // This should be a reference to where the file / page is stored (S3, Google Cloud Storage, etc.)
  storageType   DocumentStorageType @default(VERCEL_BLOB)
  createdAt     DateTime            @default(now())
  updatedAt     DateTime            @updatedAt

  @@unique([pageNumber, versionId])
  @@index([versionId])
}

enum DocumentStorageType {
  S3_PATH
  VERCEL_BLOB
}

enum ApiAuthType {
  NONE
  API_KEY
  BEARER_TOKEN
  BASIC_AUTH
  OAUTH2
  CUSTOM
}

model Domain {
  id          String   @id @default(cuid())
  slug        String   @unique
  user        User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  userId      String?
  teamId      String
  Team        Team     @relation(fields: [teamId], references: [id], onDelete: Cascade)
  verified    Boolean  @default(false) // Whether the domain has been verified
  isDefault   Boolean  @default(false) // Whether the domain is the primary domain
  lastChecked DateTime @default(now())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  links       Link[] // links associated with this domain

  @@index([userId])
  @@index([teamId])
}

model View {
  id                  String               @id @default(cuid())
  link                Link                 @relation(fields: [linkId], references: [id])
  linkId              String
  document            Document?            @relation(fields: [documentId], references: [id], onDelete: Cascade)
  documentId          String?
  dataroom            Dataroom?            @relation(fields: [dataroomId], references: [id], onDelete: Cascade)
  dataroomId          String?
  dataroomViewId      String? // This is the view ID from the dataroom
  viewerEmail         String? // Email of the viewer if known
  viewerName          String? // Name of the viewer if known
  verified            Boolean              @default(false) // Whether the viewer email has been verified
  viewedAt            DateTime             @default(now())
  downloadedAt        DateTime? // This is the time the document was downloaded
  reactions           Reaction[]
  viewType            ViewType             @default(DOCUMENT_VIEW)
  viewerId            String? // This is the viewer ID from the dataroom
  viewer              Viewer?              @relation(fields: [viewerId], references: [id], onDelete: Cascade)
  groupId             String? // This is the group ID from the dataroom
  group               ViewerGroup?         @relation(fields: [groupId], references: [id], onDelete: SetNull)
  feedbackResponse    FeedbackResponse?
  agreementResponse   AgreementResponse?
  customFieldResponse CustomFieldResponse?

  isArchived Boolean @default(false) // Indicates if the view is archived and not counted in the analytics

  // conversation
  conversationViews    ConversationView[]
  messages             Message[]
  initialConversations Conversation[]     @relation("initialView")

  uploadedDocuments DocumentUpload[] // uploaded documents by this view

  teamId String?
  team   Team?   @relation(fields: [teamId], references: [id], onDelete: Cascade)

  @@index([linkId])
  @@index([documentId])
  @@index([dataroomId])
  @@index([dataroomViewId])
  @@index([teamId])
}

enum ViewType {
  DOCUMENT_VIEW
  DATAROOM_VIEW
}

model Viewer {
  id                      String    @id @default(cuid())
  email                   String
  verified                Boolean   @default(false) // Whether the viewer email has been verified
  invitedAt               DateTime? // This is the time the viewer was invited
  notificationPreferences Json? // Format: { dataroom: {"dr_123": { "enabled": false }, "dr_456": { "enabled": true } } } }

  dataroomId String?
  dataroom   Dataroom? @relation(fields: [dataroomId], references: [id], onDelete: SetNull)
  teamId     String
  team       Team      @relation(fields: [teamId], references: [id], onDelete: Cascade)

  views                     View[]
  groups                    ViewerGroupMembership[]
  participatedConversations ConversationParticipant[]
  messages                  Message[]

  uploadedDocuments DocumentUpload[] // uploaded documents by this viewer

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([teamId, email])
  @@index([teamId])
  @@index([dataroomId])
}

model Reaction {
  id         String   @id @default(cuid())
  view       View     @relation(fields: [viewId], references: [id], onDelete: Cascade)
  viewId     String
  pageNumber Int
  type       String // e.g., "like", "dislike", "love", "hate", etc.
  createdAt  DateTime @default(now())

  @@index([viewId])
}

model Invitation {
  email     String
  expires   DateTime
  teamId    String
  team      Team     @relation(fields: [teamId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
  token     String   @unique

  @@unique([email, teamId])
}

enum EmailType {
  FIRST_DAY_DOMAIN_REMINDER_EMAIL
  FIRST_DOMAIN_INVALID_EMAIL
  SECOND_DOMAIN_INVALID_EMAIL
  FIRST_TRIAL_END_REMINDER_EMAIL
  FINAL_TRIAL_END_REMINDER_EMAIL
}

model SentEmail {
  id         String    @id @default(cuid())
  type       EmailType
  recipient  String // Email address of the recipient
  marketing  Boolean   @default(false)
  createdAt  DateTime  @default(now())
  team       Team      @relation(fields: [teamId], references: [id], onDelete: Cascade)
  teamId     String
  domainSlug String? // Domain that triggered the email. This can be nullable, representing emails not triggered by domains

  @@index([teamId])
}

model Chat {
  id            String    @id @default(cuid())
  threadId      String    @unique // This is the thread ID from OpenAI Assistant API
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId        String
  document      Document  @relation(fields: [documentId], references: [id], onDelete: Cascade)
  documentId    String
  createdAt     DateTime  @default(now())
  lastMessageAt DateTime? // This is the last time a message was sent in the thread

  @@unique([userId, documentId])
  @@unique([threadId, documentId])
  @@index([threadId])
}

model Folder {
  id           String     @id @default(cuid())
  name         String
  path         String // the materialized path to the folder; starts always with "/"
  parentId     String?
  documents    Document[]
  childFolders Folder[]   @relation("SubFolders")
  parentFolder Folder?    @relation("SubFolders", fields: [parentId], references: [id], onDelete: SetNull)
  teamId       String
  team         Team       @relation(fields: [teamId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([teamId, path])
  @@index([parentId])
}

model Feedback {
  id     String @id @default(cuid())
  linkId String @unique
  link   Link   @relation(fields: [linkId], references: [id], onDelete: Cascade)
  data   Json // This will store the feedback question data: {question: "What is the purpose of this document?", type: "yes/no", options: ["Yes", "No"]}

  responses FeedbackResponse[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([linkId])
}

model FeedbackResponse {
  id         String   @id @default(cuid())
  feedbackId String
  feedback   Feedback @relation(fields: [feedbackId], references: [id], onDelete: Cascade)
  data       Json // This will store the feedback question data: {question: "What is the purpose of this document?", type: "yes/no", options: ["Yes", "No"], answer: "Yes"}
  viewId     String   @unique
  view       View     @relation(fields: [viewId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([feedbackId])
  @@index([viewId])
}

model Agreement {
  id      String @id @default(cuid())
  name    String // Easily identifiable name for the agreement
  content String // This will store the agreement content

  links     Link[]
  responses AgreementResponse[]

  requireName Boolean @default(true) // Optional require name field

  teamId String
  team   Team   @relation(fields: [teamId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  deletedAt DateTime?
  deletedBy String?

  @@index([teamId])
}

model AgreementResponse {
  id          String    @id @default(cuid())
  agreementId String
  agreement   Agreement @relation(fields: [agreementId], references: [id], onDelete: Cascade)
  viewId      String    @unique
  view        View      @relation(fields: [viewId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([agreementId])
  @@index([viewId])
}

model IncomingWebhook {
  id                  String    @id @default(cuid())
  externalId          String    @unique
  name                String
  secret              String? // Webhook signing secret for verification
  source              String? // Allowed source URL/domain
  actions             String? // comma separated (Eg: "documents:write,documentVersions:write")
  consecutiveFailures Int       @default(0)
  lastFailedAt        DateTime?
  disabledAt          DateTime?

  teamId String
  team   Team   @relation(fields: [teamId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([teamId])
}

model RestrictedToken {
  id         String    @id @default(cuid())
  name       String
  hashedKey  String    @unique
  partialKey String
  scopes     String? // comma separated (Eg: "documents:write,links:write")
  expires    DateTime?
  lastUsed   DateTime?
  rateLimit  Int       @default(60) // rate limit per minute

  userId String
  teamId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  team   Team   @relation(fields: [teamId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([teamId])
}

model Webhook {
  id       String @id @default(cuid())
  pId      String @unique // public ID for the webhook
  name     String
  url      String
  secret   String // signing secret for the webhook
  triggers Json

  teamId String
  team   Team   @relation(fields: [teamId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([teamId])
}

model YearInReview {
  id            String    @id @default(cuid())
  teamId        String
  status        String    @default("pending") // pending, processing, completed, failed
  attempts      Int       @default(0)
  lastAttempted DateTime?
  error         String?

  stats Json

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([status, attempts])
  @@index([teamId])
}

model DocumentUpload {
  id         String   @id @default(cuid())
  documentId String
  document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  teamId     String
  team       Team     @relation(fields: [teamId], references: [id], onDelete: Cascade)
  viewerId   String?
  viewer     Viewer?  @relation(fields: [viewerId], references: [id], onDelete: SetNull)
  viewId     String?
  view       View?    @relation(fields: [viewId], references: [id], onDelete: SetNull)
  linkId     String
  link       Link     @relation(fields: [linkId], references: [id], onDelete: Cascade)

  // Optional dataroom relations
  dataroomId         String?
  dataroom           Dataroom?         @relation(fields: [dataroomId], references: [id], onDelete: SetNull)
  dataroomDocumentId String?
  dataroomDocument   DataroomDocument? @relation(fields: [dataroomDocumentId], references: [id], onDelete: SetNull)

  // Additional metadata
  originalFilename String?
  fileSize         Int?
  numPages         Int?
  mimeType         String?
  uploadedAt       DateTime @default(now())

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([documentId])
  @@index([viewerId])
  @@index([viewId])
  @@index([linkId])
  @@index([teamId])
  @@index([dataroomId])
  @@index([dataroomDocumentId])
}

enum TagType {
  LINK_TAG
  DOCUMENT_TAG
  DATAROOM_TAG
}

model Tag {
  id          String  @id @default(cuid())
  name        String
  color       String
  description String?

  teamId String
  team   Team   @relation(fields: [teamId], references: [id], onDelete: Cascade)

  items TagItem[]

  createdBy String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([teamId, name])
  @@index([teamId])
  @@index([name])
  @@index([id])
}

model TagItem {
  id       String  @id @default(cuid())
  tagId    String
  tag      Tag     @relation(fields: [tagId], references: [id], onDelete: Cascade)
  itemType TagType

  // tag can be linked to a link, document or dataroom
  linkId     String?
  link       Link?     @relation(fields: [linkId], references: [id], onDelete: Cascade)
  documentId String?
  document   Document? @relation(fields: [documentId], references: [id], onDelete: Cascade)
  dataroomId String?
  dataroom   Dataroom? @relation(fields: [dataroomId], references: [id], onDelete: Cascade)

  taggedBy  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([tagId, linkId])
  @@index([tagId, documentId])
  @@index([tagId, dataroomId])
}
