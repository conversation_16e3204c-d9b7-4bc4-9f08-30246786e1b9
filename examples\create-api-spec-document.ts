/**
 * Example script showing how to create an API specification document
 * using the new API specification fields in the Document model.
 */

import { ApiAuthType, DocumentStorageType } from "@prisma/client";
import { DocumentData } from "../lib/documents/create-document";

// Example API specification data
const exampleApiSpecData: DocumentData = {
  name: "Stripe API v1",
  key: "stripe-api-spec.json", // This would be the actual file URL/key
  storageType: DocumentStorageType.VERCEL_BLOB,
  contentType: "application/json",
  supportedFileType: "api", // New file type for API specifications
  fileSize: 1024 * 50, // 50KB
  numPages: undefined, // API specs don't have pages
  enableExcelAdvancedMode: false,
  
  // API Specification specific fields
  isApiSpec: true,
  baseUrl: "https://api.stripe.com/v1",
  apiVersion: "2023-10-16",
  authType: ApiAuthType.BEARER_TOKEN,
  authConfig: {
    type: "bearer",
    scheme: "bearer",
    description: "Use your Stripe secret key as the bearer token",
    headerName: "Authorization",
    prefix: "Bearer "
  },
  endpoints: [
    {
      path: "/customers",
      method: "GET",
      summary: "List all customers",
      description: "Returns a list of your customers",
      parameters: [
        {
          name: "limit",
          in: "query",
          type: "integer",
          description: "A limit on the number of objects to be returned",
          default: 10
        },
        {
          name: "starting_after",
          in: "query",
          type: "string",
          description: "A cursor for use in pagination"
        }
      ],
      responses: {
        "200": {
          description: "A list of customers",
          schema: {
            type: "object",
            properties: {
              object: { type: "string", example: "list" },
              data: {
                type: "array",
                items: { $ref: "#/definitions/Customer" }
              },
              has_more: { type: "boolean" },
              url: { type: "string" }
            }
          }
        }
      }
    },
    {
      path: "/customers",
      method: "POST",
      summary: "Create a customer",
      description: "Creates a new customer object",
      parameters: [
        {
          name: "email",
          in: "body",
          type: "string",
          required: true,
          description: "Customer's email address"
        },
        {
          name: "name",
          in: "body",
          type: "string",
          description: "Customer's full name"
        },
        {
          name: "description",
          in: "body",
          type: "string",
          description: "An arbitrary string attached to the object"
        }
      ],
      responses: {
        "200": {
          description: "The created customer object",
          schema: { $ref: "#/definitions/Customer" }
        }
      }
    },
    {
      path: "/customers/{id}",
      method: "GET",
      summary: "Retrieve a customer",
      description: "Retrieves the details of an existing customer",
      parameters: [
        {
          name: "id",
          in: "path",
          type: "string",
          required: true,
          description: "The customer ID"
        }
      ],
      responses: {
        "200": {
          description: "The customer object",
          schema: { $ref: "#/definitions/Customer" }
        },
        "404": {
          description: "Customer not found"
        }
      }
    }
  ]
};

// Example of different authentication types
const apiKeyAuthExample = {
  authType: ApiAuthType.API_KEY,
  authConfig: {
    type: "apiKey",
    name: "X-API-Key",
    in: "header",
    description: "API key for authentication"
  }
};

const basicAuthExample = {
  authType: ApiAuthType.BASIC_AUTH,
  authConfig: {
    type: "basic",
    description: "HTTP Basic Authentication"
  }
};

const oauth2Example = {
  authType: ApiAuthType.OAUTH2,
  authConfig: {
    type: "oauth2",
    flow: "authorizationCode",
    authorizationUrl: "https://api.example.com/oauth/authorize",
    tokenUrl: "https://api.example.com/oauth/token",
    scopes: {
      "read": "Read access to resources",
      "write": "Write access to resources",
      "admin": "Admin access to all resources"
    }
  }
};

// Function to create an API specification document
export async function createApiSpecDocument(
  teamId: string,
  apiSpecData: DocumentData,
  token?: string
) {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL}/api/teams/${teamId}/documents`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify({
        name: apiSpecData.name,
        url: apiSpecData.key,
        storageType: apiSpecData.storageType,
        type: apiSpecData.supportedFileType,
        contentType: apiSpecData.contentType,
        fileSize: apiSpecData.fileSize,
        isApiSpec: apiSpecData.isApiSpec,
        baseUrl: apiSpecData.baseUrl,
        apiVersion: apiSpecData.apiVersion,
        authType: apiSpecData.authType,
        authConfig: apiSpecData.authConfig,
        endpoints: apiSpecData.endpoints,
      }),
    }
  );

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
}

// Example usage:
// const document = await createApiSpecDocument("team_123", exampleApiSpecData);
// console.log("Created API specification document:", document);

export {
  exampleApiSpecData,
  apiKeyAuthExample,
  basicAuthExample,
  oauth2Example
};
