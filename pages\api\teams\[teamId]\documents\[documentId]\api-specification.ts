import { NextApiRequest, NextApiResponse } from "next";
import { getServerSession } from "next-auth/next";

import { authOptions } from "@/pages/api/auth/[...nextauth]";
import { getApiSpecificationByDocumentId } from "@/lib/api/api-specifications/create-api-specification";
import { errorhandler } from "@/lib/errorHandler";
import { CustomUser } from "@/lib/types";
import { getTeamWithUsersAndDocument } from "@/lib/team/helper";

export default async function handle(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method === "GET") {
    // GET /api/teams/:teamId/documents/:documentId/api-specification
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).end("Unauthorized");
    }

    const { teamId, documentId } = req.query as { 
      teamId: string; 
      documentId: string; 
    };
    const userId = (session.user as CustomUser).id;

    try {
      // Verify user is part of the team
      await getTeamWithUsersAndDocument({
        teamId,
        userId,
      });

      const apiSpecification = await getApiSpecificationByDocumentId(
        documentId,
        teamId
      );

      if (!apiSpecification) {
        return res.status(404).json({ 
          error: "API specification not found for this document" 
        });
      }

      return res.status(200).json(apiSpecification);
    } catch (error) {
      errorhandler(error, res);
    }
  } else {
    // We only allow GET requests
    res.setHeader("Allow", ["GET"]);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
