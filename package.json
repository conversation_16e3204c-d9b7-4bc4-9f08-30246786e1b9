{"name": "papermark", "version": "0.1.0", "private": true, "engines": {"node": ">=18.18.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate", "vercel-build": "prisma migrate deploy && next build", "email": "email dev --dir ./components/emails --port 3001", "trigger:v3:dev": "npx trigger.dev@latest dev", "trigger:v3:deploy": "npx trigger.dev@latest deploy", "stripe:webhook": "pkgx stripe listen --forward-to localhost:3000/api/stripe/webhook", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,mdx}\"", "dev:prisma": "npx prisma generate && npx prisma migrate deploy"}, "dependencies": {"@aws-sdk/client-lambda": "^3.817.0", "@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/cloudfront-signer": "^3.813.0", "@aws-sdk/lib-storage": "^3.817.0", "@aws-sdk/s3-request-presigner": "^3.817.0", "@chronark/zod-bird": "^0.3.10", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@github/webauthn-json": "^2.1.1", "@jitsu/js": "^1.10.0", "@next-auth/prisma-adapter": "^1.0.7", "@pdf-lib/fontkit": "^1.1.1", "@prisma/client": "^6.5.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-portal": "^1.1.9", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.0.39", "@sindresorhus/slugify": "^2.2.1", "@stripe/stripe-js": "^4.10.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-table": "^8.21.3", "@teamhanko/passkeys-next-auth-provider": "^0.3.1", "@tremor/react": "^3.18.7", "@trigger.dev/react-hooks": "^3.3.17", "@trigger.dev/sdk": "^3.3.17", "@tus/s3-store": "^1.9.1", "@tus/server": "^1.10.2", "@tus/utils": "^0.5.1", "@upstash/qstash": "^2.8.1", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.9", "@vercel/blob": "^0.23.4", "@vercel/edge-config": "^1.4.0", "@vercel/functions": "^2.1.0", "ai": "2.2.37", "autoprefixer": "^10.4.21", "base-x": "^5.0.1", "bcryptjs": "^3.0.2", "bottleneck": "^2.19.5", "chrono-node": "^2.8.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "eslint": "8.57.0", "eslint-config-next": "^14.2.29", "exceljs": "^4.4.0", "fluent-ffmpeg": "^2.1.3", "handlebars": "^4.7.8", "input-otp": "^1.4.2", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "mime-types": "^3.0.1", "motion": "^12.13.0", "ms": "^2.1.3", "mupdf": "^1.3.6", "nanoid": "^5.1.5", "next": "^14.2.29", "next-auth": "^4.24.11", "next-plausible": "^3.12.4", "next-themes": "^0.4.6", "notion-client": "^7.3.0", "notion-utils": "^7.3.0", "nuqs": "^2.4.3", "openai": "4.20.1", "pdf-lib": "^1.17.1", "postcss": "^8.5.3", "posthog-js": "^1.246.0", "react": "^18.3.1", "react-colorful": "^5.6.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-dropzone": "^14.3.8", "react-email": "^4.0.15", "react-hotkeys-hook": "^5.1.0", "react-intersection-observer": "^9.16.0", "react-notion-x": "^7.3.0", "react-pdf": "^8.0.2", "react-phone-number-input": "^3.4.12", "react-syntax-highlighter": "^15.6.1", "react-textarea-autosize": "^8.5.9", "react-zoom-pan-pinch": "^3.7.0", "resend": "^4.3.0", "sanitize-html": "^2.17.0", "sonner": "^2.0.3", "stripe": "^16.12.0", "swr": "^2.3.3", "tailwind-merge": "^2.6.0", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "ts-pattern": "^5.7.1", "tus-js-client": "^4.3.1", "ua-parser-js": "^1.0.40", "unsend": "^1.5.1", "use-debounce": "^10.0.4", "vaul": "^1.1.2", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "zod": "^3.25.28"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@trigger.dev/build": "^3.3.17", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/bcryptjs": "^2.4.6", "@types/cookie": "^0.6.0", "@types/fluent-ffmpeg": "^2.1.27", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.7", "@types/mime-types": "^2.1.4", "@types/ms": "^2.1.0", "@types/node": "^22.13.5", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-syntax-highlighter": "^15.5.13", "@types/sanitize-html": "^2.13.0", "@types/ua-parser-js": "^0.7.39", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.5.0", "typescript": "^5"}, "overrides": {"react-notion-x": {"react-pdf": "8.0.2"}}}